Metadata-Version: 2.1
Name: Send2Trash
Version: 1.8.3
Summary: Send file to trash natively under Mac OS X, Windows and Linux
Home-page: https://github.com/arsenetar/send2trash
Author: <PERSON>
Author-email: <EMAIL>
License: BSD License
Project-URL: Bug Reports, https://github.com/arsenetar/send2trash/issues
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Desktop Environment :: File Managers
Requires-Python: !=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,>=2.7
Description-Content-Type: text/x-rst
License-File: LICENSE
Provides-Extra: nativelib
Requires-Dist: pyobjc-framework-Cocoa ; (sys_platform == "darwin") and extra == 'nativelib'
Requires-Dist: pywin32 ; (sys_platform == "win32") and extra == 'nativelib'
Provides-Extra: objc
Requires-Dist: pyobjc-framework-Cocoa ; (sys_platform == "darwin") and extra == 'objc'
Provides-Extra: win32
Requires-Dist: pywin32 ; (sys_platform == "win32") and extra == 'win32'

==================================================
Send2Trash -- Send files to trash on all platforms
==================================================

Send2Trash is a small package that sends files to the Trash (or Recycle Bin) *natively* and on
*all platforms*. On OS X, it uses native ``FSMoveObjectToTrashSync`` Cocoa calls or can use pyobjc 
with NSFileManager. On Windows, it uses native ``IFileOperation`` call if on Vista or newer and 
pywin32 is installed or falls back to ``SHFileOperation`` calls. On other platforms, if `PyGObject`_ 
and `GIO`_ are available, it will use this.  Otherwise, it will fallback to its own implementation of 
the `trash specifications from freedesktop.org`_.

``ctypes`` is used to access native libraries, so no compilation is necessary.

Send2Trash supports Python 2.7 and up (Python 3 is supported).

Status: Additional Help Welcome
-------------------------------

Additional help is welcome for supporting this package.  Specifically help with the OSX and Linux 
issues and fixes would be most appreciated.

Installation
------------

You can download it with pip:

    python -m pip install -U send2trash

To install with pywin32 or pyobjc required specify the extra `nativeLib`:

    python -m pip install -U send2trash[nativeLib]

or you can download the source from http://github.com/arsenetar/send2trash and install it with::

    >>> python setup.py install

Usage
-----

>>> from send2trash import send2trash
>>> send2trash('some_file')
>>> send2trash(['some_file1', 'some_file2'])

On Freedesktop platforms (Linux, BSD, etc.), you may not be able to efficiently
trash some files. In these cases, an exception ``send2trash.TrashPermissionError``
is raised, so that the application can handle this case. This inherits from
``PermissionError`` (``OSError`` on Python 2). Specifically, this affects
files on a different device to the user's home directory, where the root of the
device does not have a ``.Trash`` directory, and we don't have permission to
create a ``.Trash-$UID`` directory.

For any other problem, ``OSError`` is raised.

.. _PyGObject: https://wiki.gnome.org/PyGObject
.. _GIO: https://developer.gnome.org/gio/
.. _trash specifications from freedesktop.org: http://freedesktop.org/wiki/Specifications/trash-spec/
