# automatically generated by the FlatBuffers compiler, do not modify

# namespace: wamp

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class Map(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = Map()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsMap(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # Map
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # Map
    def Key(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # Map
    def Value(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

def MapStart(builder): builder.StartObject(2)
def Start(builder):
    return MapStart(builder)
def MapAddKey(builder, key): builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(key), 0)
def AddKey(builder, key):
    return MapAddKey(builder, key)
def MapAddValue(builder, value): builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(value), 0)
def AddValue(builder, value):
    return MapAddValue(builder, value)
def MapEnd(builder): return builder.EndObject()
def End(builder):
    return MapEnd(builder)