# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class AuthTicketRequest(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = AuthTicketRequest()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsAuthTicketRequest(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # AuthTicketRequest
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

def AuthTicketRequestStart(builder): builder.StartObject(0)
def Start(builder):
    return AuthTicketRequestStart(builder)
def AuthTicketRequestEnd(builder): return builder.EndObject()
def End(builder):
    return AuthTicketRequestEnd(builder)